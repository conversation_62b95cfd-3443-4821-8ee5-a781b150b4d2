import { Injectable } from '@angular/core';
import { Router, Routes } from '@angular/router';
import { ConfigService } from './config.service';
import { AuthGuard, GuestGuard } from '../guards';

@Injectable({
  providedIn: 'root',
})
export class RouteService {
  private componentModules: Record<string, () => Promise<any>> = {};

  constructor(
    private router: Router,
    private configService: ConfigService,
  ) {
    this.initializeComponentModules();
  }

  generateDynamicRoutes(): Routes {
    const baseRoutes: Routes = [
      {
        path: '',
        redirectTo: this.getDefaultRoute(),
        pathMatch: 'full',
      },
      {
        path: 'auth',
        canActivate: [GuestGuard],
        children: [
          {
            path: 'login',
            loadComponent: () =>
              import('../../auth/login/login.component').then(
                (m) => m.LoginComponent,
              ),
          },
          {
            path: '',
            redirectTo: 'login',
            pathMatch: 'full',
          },
        ],
      },
      {
        path: 'unauthorized',
        loadComponent: () =>
          import('../../pages/unauthorized/unauthorized.component').then(
            (m) => m.UnauthorizedComponent,
          ),
      },
    ];

    // Add dynamic routes from config
    const dynamicRoutes = this.configService.navigation.map((feature) => ({
      path: feature.route.substring(1), // Remove leading slash
      canActivate: [AuthGuard],
      loadComponent: () => this.getComponentLoader(feature.id),
    }));

    // Add catch-all route
    const catchAllRoute: Routes = [
      {
        path: '**',
        redirectTo: this.getDefaultRoute(),
      },
    ];

    return [...baseRoutes, ...dynamicRoutes, ...catchAllRoute];
  }

  updateRoutes(): void {
    const newRoutes = this.generateDynamicRoutes();
    this.router.resetConfig(newRoutes);
    console.log(
      '🔄 Routes updated based on config:',
      newRoutes.length,
      'routes',
    );
  }

  private getDefaultRoute(): string {
    const firstFeature = this.configService.navigation[0];
    return firstFeature ? firstFeature.route.substring(1) : 'ad-winners';
  }

  private initializeComponentModules(): void {
    console.log('🔧 Initializing component modules for Angular...');

    // Angular-compatible component registration
    // Auto-register based on config navigation items
    this.componentModules = {};

    for (const feature of this.configService.navigation) {
      const navId = feature.id;

      // Register known components
      switch (navId) {
        case 'ad-winners':
          this.componentModules[navId] = () =>
            import('../../pages/ad-winners/ad-winners.component').then((m) => ({
              AdWinnersComponent: m.AdWinnersComponent,
            }));
          break;

        default:
          console.warn(`⚠️ No component loader defined for: ${navId}`);
          // Create a placeholder loader that returns PageNotFoundComponent
          this.componentModules[navId] = () => this.getFallbackComponent();
          break;
      }

      console.log(`📦 Registered component loader for: ${navId}`);
    }

    console.log(
      '🎯 Final component modules:',
      Object.keys(this.componentModules),
    );
  }

  private async getComponentLoader(navId: string): Promise<any> {
    try {
      console.log(`🔄 Loading component for navId: ${navId}`);

      if (this.componentModules[navId]) {
        const module = await this.componentModules[navId]();
        const componentName = this.navIdToComponentName(navId);

        if (module[componentName]) {
          console.log(`✅ Successfully loaded: ${componentName}`);
          return module[componentName];
        } else {
          console.warn(
            `⚠️ Component ${componentName} not found in module, available:`,
            Object.keys(module),
          );
          throw new Error(`Component ${componentName} not exported`);
        }
      } else {
        console.warn(`⚠️ No component module found for navId: ${navId}`);
        console.log('Available modules:', Object.keys(this.componentModules));
        throw new Error(`No component module found for ${navId}`);
      }
    } catch (error) {
      console.error(`❌ Failed to load component for ${navId}:`, error);

      // Fallback to a default component
      return this.getFallbackComponent();
    }
  }

  private navIdToComponentName(navId: string): string {
    // Convert 'ad-winners' -> 'AdWinnersComponent'
    // Convert 'settings' -> 'SettingsComponent'
    // Convert 'user-profile' -> 'UserProfileComponent'

    return (
      navId
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join('') + 'Component'
    );
  }

  private async getFallbackComponent(): Promise<any> {
    // Use the dedicated PageNotFoundComponent
    try {
      const module = await import(
        '../../shared/components/page-not-found/page-not-found.component'
      );
      return module.PageNotFoundComponent;
    } catch {
      // If even the fallback component fails, return ad-winners
      const module = await import(
        '../../pages/ad-winners/ad-winners.component'
      );
      return module.AdWinnersComponent;
    }
  }
}
