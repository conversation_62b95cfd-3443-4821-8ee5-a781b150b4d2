/* Import shared styles */
@import '../../shared/styles/page-layout.css';

/* Component specific overrides */
.creatives-uploader-container {
  /* Use shared page-container class */
}

/* Header adjustments for creatives uploader */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  text-align: left;
}

.header-info {
  flex: 1;
}

/* Active Uploads Indicator */
.active-uploads-indicator .active-uploads-tag {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.active-uploads-indicator .active-uploads-tag i {
  margin-right: 0.5rem;
}

/* Filters specific to creatives uploader */
.filters-grid {
  grid-template-columns: 200px 200px 1fr 150px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-item.search-item {
  min-width: 250px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
