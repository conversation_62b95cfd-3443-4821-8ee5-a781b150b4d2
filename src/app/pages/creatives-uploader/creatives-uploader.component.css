/* Import shared styles */
@import '../../shared/styles/page-layout.css';

/* Component specific overrides */
.creatives-uploader-container {
  /* Use shared page-container class */
}

/* Header adjustments for creatives uploader */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  text-align: center;
}

.header-info {
  flex: 1;
}

/* Active Uploads Indicator */
.active-uploads-indicator .active-uploads-tag {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.active-uploads-indicator .active-uploads-tag i {
  margin-right: 0.5rem;
}

/* Filters specific to creatives uploader */
.filters-grid {
  grid-template-columns: 200px 200px 1fr 150px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-item.search-item {
  min-width: 250px;
}

/* Fix search input height and icon positioning */
.search-input {
  height: 42px !important;
  line-height: 1.5 !important;
}

.p-input-icon-left {
  position: relative;
}

.p-input-icon-left > i {
  position: absolute;
  top: 50%;
  left: 0.75rem;
  transform: translateY(-50%);
  z-index: 2;
  color: #9ca3af;
  transition: color 0.3s ease;
  font-size: 0.875rem;
}

.p-input-icon-left:hover > i,
.p-input-icon-left:focus-within > i {
  color: #5521be;
}

/* Fix dropdown z-index to appear above stats cards */
::ng-deep .p-dropdown-panel {
  z-index: 9999 !important;
}

::ng-deep .p-multiselect-panel {
  z-index: 9999 !important;
}

::ng-deep .p-dropdown {
  z-index: 1000 !important;
}

::ng-deep .p-dropdown .p-dropdown-panel {
  z-index: 9999 !important;
}



/* Type column styling */
.type-cell {
  text-align: center;
  width: 60px;
}

.file-type-icon {
  font-size: 1.5rem;
}

/* Actions column styling */
.actions-cell {
  text-align: center;
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

.action-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #6b7280;
}

.action-link:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-link.facebook {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
}

.action-link.facebook:hover {
  background: linear-gradient(135deg, #166fe5 0%, #1976d2 100%);
}

.action-link.google-drive {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
}

.action-link.google-drive:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2e7d32 100%);
}

.action-link i {
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
