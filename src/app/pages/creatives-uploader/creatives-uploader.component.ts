import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { interval, startWith, Subject, switchMap, takeUntil } from 'rxjs';
import { MessageService } from 'primeng/api';

import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

import {
  CREATIVE_FILE_STATUSES,
  CreativeFile,
  CreativeFileFilters,
  CreativeFileStatus,
  formatFileSize,
  getMimeTypeIcon,
  getMimeTypeLabel,
  getStatusLabel,
  getStatusSeverity,
  isActiveStatus,
  MIME_TYPE_FILTERS,
} from './models';
import { CreativeFileService } from './services';

@Component({
  selector: 'chm-creatives-uploader',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    TagModule,
    ProgressBarModule,
    ProgressSpinnerModule,
    ToastModule,
  ],
  providers: [MessageService],
  templateUrl: './creatives-uploader.component.html',
  styleUrls: ['./creatives-uploader.component.css'],
})
export class CreativesUploaderComponent implements OnInit, OnDestroy {
  // Data
  files: CreativeFile[] = [];
  totalRecords = 0;
  loading = false;
  // Filters
  filters: CreativeFileFilters = {};
  statusOptions = CREATIVE_FILE_STATUSES;
  mimeTypeOptions = MIME_TYPE_FILTERS;
  // Template helper methods
  getStatusSeverity = getStatusSeverity;
  getStatusLabel = getStatusLabel;
  formatFileSize = formatFileSize;
  getMimeTypeIcon = getMimeTypeIcon;
  getMimeTypeLabel = getMimeTypeLabel;
  isActiveStatus = isActiveStatus;
  private destroy$ = new Subject<void>();
  // Auto-reload
  private autoReloadInterval = 30000; // 30 seconds

  constructor(
    private creativeFileService: CreativeFileService,
    private messageService: MessageService,
  ) {}

  ngOnInit(): void {
    this.loadFiles();
    this.setupAutoReload();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadFiles(): void {
    this.loading = true;

    this.creativeFileService
      .getCreativeFiles(this.filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.files = response.data;
          this.totalRecords = response.total_count;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading files:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load files',
          });
          this.loading = false;
        },
      });
  }

  onFilterChange(): void {
    this.loadFiles();
  }

  onStatusFilterChange(status: CreativeFileStatus | null): void {
    this.filters.status = status || undefined;
    this.loadFiles();
  }

  onMimeTypeFilterChange(mimeType: string): void {
    this.filters.mime_type = mimeType || undefined;
    this.loadFiles();
  }

  onSearchChange(event: any): void {
    this.filters.search = event.target.value || undefined;
    this.loadFiles();
  }

  retryUpload(file: CreativeFile): void {
    this.creativeFileService
      .retryUpload(file.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'info',
            summary: 'Retry',
            detail: `Retrying upload for ${file.file_name}`,
          });
          this.loadFiles();
        },
        error: (error) => {
          console.error('Error retrying upload:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to retry upload',
          });
        },
      });
  }

  deleteFile(file: CreativeFile): void {
    this.creativeFileService
      .deleteFile(file.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Deleted',
            detail: `${file.file_name} deleted successfully`,
          });
          this.loadFiles();
        },
        error: (error) => {
          console.error('Error deleting file:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to delete file',
          });
        },
      });
  }

  getActiveUploadsCount(): number {
    return this.creativeFileService.getActiveUploadsCount(this.files);
  }

  hasActiveUploads(): boolean {
    return this.creativeFileService.hasActiveUploads(this.files);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getFileTypeClass(mimeType: string): string {
    if (mimeType.startsWith('video/')) return 'file-type-video';
    if (mimeType.startsWith('image/')) return 'file-type-image';
    return 'file-type-other';
  }

  canRetry(file: CreativeFile): boolean {
    return file.status === 'failed';
  }

  canDelete(file: CreativeFile): boolean {
    return file.status === 'failed' || file.status === 'uploaded';
  }

  getProgressValue(status: CreativeFileStatus): number {
    switch (status) {
      case 'pending':
        return 10;
      case 'downloading':
        return 40;
      case 'uploading':
        return 80;
      case 'uploaded':
        return 100;
      case 'failed':
        return 0;
      default:
        return 0;
    }
  }

  getProgressClass(status: CreativeFileStatus): string {
    switch (status) {
      case 'pending':
        return 'p-progressbar-secondary';
      case 'downloading':
        return 'p-progressbar-info';
      case 'uploading':
        return 'p-progressbar-warning';
      case 'uploaded':
        return 'p-progressbar-success';
      case 'failed':
        return 'p-progressbar-danger';
      default:
        return 'p-progressbar-secondary';
    }
  }

  private setupAutoReload(): void {
    // Auto-reload when there are active uploads
    interval(this.autoReloadInterval)
      .pipe(
        startWith(0),
        switchMap(() => {
          const hasActiveUploads = this.creativeFileService.hasActiveUploads(
            this.files,
          );
          return hasActiveUploads ? [true] : [];
        }),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        if (this.creativeFileService.hasActiveUploads(this.files)) {
          this.loadFiles();
        }
      });
  }
}
