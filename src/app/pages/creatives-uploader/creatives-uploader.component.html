<div class="page-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-info">
        <h1 class="page-title">
          <i class="pi pi-cloud-upload"></i>
          Creative Files Upload
        </h1>
        <p class="page-description">
          Manage your Google Drive to Facebook creative uploads
        </p>
      </div>

      <!-- Active Uploads Indicator -->
      <div *ngIf="hasActiveUploads()" class="active-uploads-indicator">
        <p-tag
          [value]="getActiveUploadsCount() + ' Active Uploads'"
          severity="info"
          styleClass="active-uploads-tag">
          <i class="pi pi-spin pi-spinner"></i>
        </p-tag>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <div class="filters-grid">
      <!-- Status Filter -->
      <div class="filter-item">
        <label class="filter-label">Status</label>
        <p-dropdown
          (onChange)="onStatusFilterChange($event.value)"
          [options]="statusOptions"
          [showClear]="true"
          optionLabel="label"
          optionValue="value"
          placeholder="All Statuses"
          styleClass="filter-dropdown">
        </p-dropdown>
      </div>

      <!-- File Type Filter -->
      <div class="filter-item">
        <label class="filter-label">File Type</label>
        <p-dropdown
          (onChange)="onMimeTypeFilterChange($event.value)"
          [options]="mimeTypeOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="All Files"
          styleClass="filter-dropdown">
        </p-dropdown>
      </div>

      <!-- Search -->
      <div class="filter-item search-item">
        <label class="filter-label">Search</label>
        <span class="p-input-icon-left">
          <i class="pi pi-search"></i>
          <input
            (input)="onSearchChange($event)"
            class="search-input"
            pInputText
            placeholder="Search by filename..."
            type="text">
        </span>
      </div>

      <!-- Refresh Button -->
      <div class="filter-item">
        <label class="filter-label">&nbsp;</label>
        <p-button
          (onClick)="loadFiles()"
          [loading]="loading"
          icon="pi pi-refresh"
          label="Refresh"
          severity="secondary"
          styleClass="refresh-button">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div *ngIf="!loading" class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon">
        <i class="pi pi-file"></i>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ totalRecords }}</div>
        <div class="stat-label">Total Files</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon active">
        <i class="pi pi-clock"></i>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ getActiveUploadsCount() }}</div>
        <div class="stat-label">Active Uploads</div>
      </div>
    </div>
  </div>

  <!-- Files Table -->
  <div class="table-section">
    <p-table
      #dt
      [globalFilterFields]="['file_name']"
      [loading]="loading"
      [paginator]="true"
      [rowsPerPageOptions]="[10, 20, 50]"
      [rows]="20"
      [showCurrentPageReport]="true"
      [showGridlines]="true"
      [tableStyle]="{'width': '100%'}"
      [value]="files"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      stripedRows="true">

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 40px;">Type</th>
          <th>File Name</th>
          <th style="width: 100px;">Size</th>
          <th style="width: 150px;">Status</th>
          <th style="width: 200px;">Progress</th>
          <th style="width: 150px;">Created</th>
          <th style="width: 150px;">Updated</th>
          <th style="width: 120px;">Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template let-file pTemplate="body">
        <tr>
          <!-- File Type Icon -->
          <td>
            <i
              [class]="'pi ' + getMimeTypeIcon(file.mime_type) + ' ' + getFileTypeClass(file.mime_type)"
              [title]="getMimeTypeLabel(file.mime_type)">
            </i>
          </td>

          <!-- File Name -->
          <td>
            <div class="file-name-cell">
              <span [title]="file.file_name" class="file-name">{{ file.file_name }}</span>
              <small *ngIf="file.upload_error" class="error-message">
                {{ file.upload_error }}
              </small>
            </div>
          </td>

          <!-- File Size -->
          <td>
            <span class="file-size">{{ formatFileSize(file.file_size) }}</span>
          </td>

          <!-- Status -->
          <td>
            <p-tag
              [severity]="getStatusSeverity(file.status)"
              [value]="getStatusLabel(file.status)"
              styleClass="status-tag">
              <i *ngIf="isActiveStatus(file.status)" class="pi pi-spin pi-spinner status-spinner"></i>
            </p-tag>
          </td>

          <!-- Progress -->
          <td>
            <div class="progress-cell">
              <p-progressBar
                [styleClass]="getProgressClass(file.status)"
                [style]="{'height': '8px', 'margin-bottom': '4px'}"
                [value]="getProgressValue(file.status)">
              </p-progressBar>
              <small class="progress-text">{{ getStatusLabel(file.status) }}</small>
            </div>
          </td>

          <!-- Created Date -->
          <td>
            <span class="date-text">{{ formatDate(file.created_at) }}</span>
          </td>

          <!-- Updated Date -->
          <td>
            <span class="date-text">{{ formatDate(file.updated_at) }}</span>
          </td>

          <!-- Actions -->
          <td>
            <div class="action-buttons">
              <p-button
                (onClick)="retryUpload(file)"
                *ngIf="canRetry(file)"
                icon="pi pi-refresh"
                size="small"
                styleClass="action-button"
                title="Retry Upload">
              </p-button>

              <p-button
                (onClick)="deleteFile(file)"
                *ngIf="canDelete(file)"
                icon="pi pi-trash"
                severity="danger"
                size="small"
                styleClass="action-button"
                title="Delete File">
              </p-button>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8">
            <div class="empty-state">
              <i class="pi pi-cloud-upload empty-icon"></i>
              <h3>No files found</h3>
              <p>No creative files match your current filters.</p>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Loading Template -->
      <ng-template pTemplate="loadingbody">
        <tr>
          <td colspan="8">
            <div class="loading-state">
              <p-progressSpinner strokeWidth="3"></p-progressSpinner>
              <p>Loading files...</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
