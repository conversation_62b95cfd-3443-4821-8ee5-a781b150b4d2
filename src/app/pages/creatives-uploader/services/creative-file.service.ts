import { Injectable } from '@angular/core';
import { catchError, from, map, Observable, throwError } from 'rxjs';
import { SupabaseService } from '../../../core/services';
import {
  CreativeFile,
  CreativeFileFilters,
  CreativeFileResponse,
  CreativeFileStatus,
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class CreativeFileService {
  constructor(private supabaseService: SupabaseService) {}

  getCreativeFiles(
    filters: CreativeFileFilters = {},
  ): Observable<CreativeFileResponse> {
    let query = this.supabaseService.client
      .from('fb_creative_files')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.mime_type) {
      query = query.like('mime_type', `${filters.mime_type}%`);
    }

    if (filters.search) {
      query = query.ilike('file_name', `%${filters.search}%`);
    }

    return from(query).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching creative files:', response.error);
          throw response.error;
        }

        return {
          data: response.data || [],
          total_count: response.count || 0,
        };
      }),
      catchError((error) => {
        console.error('Error fetching creative files:', error);
        return throwError(() => error);
      }),
    );
  }

  updateFileStatus(
    id: string,
    status: CreativeFileStatus,
    error?: string,
  ): Observable<CreativeFile> {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    };

    if (status === 'uploaded') {
      updateData.uploaded_at = new Date().toISOString();
    }

    if (error) {
      updateData.upload_error = error;
    }

    return from(
      this.supabaseService.client
        .from('fb_creative_files')
        .update(updateData)
        .eq('id', id)
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error updating file status:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error updating file status:', error);
        return throwError(() => error);
      }),
    );
  }

  deleteFile(id: string): Observable<void> {
    return from(
      this.supabaseService.client
        .from('fb_creative_files')
        .delete()
        .eq('id', id),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error deleting file:', response.error);
          throw response.error;
        }
        return;
      }),
      catchError((error) => {
        console.error('Error deleting file:', error);
        return throwError(() => error);
      }),
    );
  }

  retryUpload(id: string): Observable<CreativeFile> {
    return this.updateFileStatus(id, 'pending');
  }

  hasActiveUploads(files: CreativeFile[]): boolean {
    return files.some(
      (file) =>
        file.status === 'downloading' ||
        file.status === 'uploading' ||
        file.status === 'pending',
    );
  }

  getActiveUploadsCount(files: CreativeFile[]): number {
    return files.filter(
      (file) =>
        file.status === 'downloading' ||
        file.status === 'uploading' ||
        file.status === 'pending',
    ).length;
  }
}
