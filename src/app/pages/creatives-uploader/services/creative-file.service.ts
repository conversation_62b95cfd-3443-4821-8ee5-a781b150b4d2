import { Injectable } from '@angular/core';
import { Observable, map, catchError, throwError } from 'rxjs';
import { SupabaseClient, createClient } from '@supabase/supabase-js';
import { environment } from '../../../../environments/environment';
import { 
  CreativeFile, 
  CreativeFileFilters, 
  CreativeFileResponse,
  CreativeFileStatus 
} from '../models';

@Injectable({
  providedIn: 'root'
})
export class CreativeFileService {
  private supabase: SupabaseClient;

  constructor() {
    this.supabase = createClient(
      environment.supabase.url,
      environment.supabase.anonKey
    );
  }

  getCreativeFiles(filters: CreativeFileFilters = {}): Observable<CreativeFileResponse> {
    let query = this.supabase
      .from('fb_creative_files')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.mime_type) {
      query = query.like('mime_type', `${filters.mime_type}%`);
    }

    if (filters.search) {
      query = query.ilike('file_name', `%${filters.search}%`);
    }

    return new Observable(observer => {
      query.then(({ data, error, count }) => {
        if (error) {
          observer.error(error);
          return;
        }

        observer.next({
          data: data || [],
          total_count: count || 0
        });
        observer.complete();
      });
    }).pipe(
      catchError(error => {
        console.error('Error fetching creative files:', error);
        return throwError(() => error);
      })
    );
  }

  updateFileStatus(id: string, status: CreativeFileStatus, error?: string): Observable<CreativeFile> {
    const updateData: any = { 
      status,
      updated_at: new Date().toISOString()
    };

    if (status === 'uploaded') {
      updateData.uploaded_at = new Date().toISOString();
    }

    if (error) {
      updateData.upload_error = error;
    }

    return new Observable(observer => {
      this.supabase
        .from('fb_creative_files')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()
        .then(({ data, error }) => {
          if (error) {
            observer.error(error);
            return;
          }

          observer.next(data);
          observer.complete();
        });
    }).pipe(
      catchError(error => {
        console.error('Error updating file status:', error);
        return throwError(() => error);
      })
    );
  }

  deleteFile(id: string): Observable<void> {
    return new Observable(observer => {
      this.supabase
        .from('fb_creative_files')
        .delete()
        .eq('id', id)
        .then(({ error }) => {
          if (error) {
            observer.error(error);
            return;
          }

          observer.next();
          observer.complete();
        });
    }).pipe(
      catchError(error => {
        console.error('Error deleting file:', error);
        return throwError(() => error);
      })
    );
  }

  retryUpload(id: string): Observable<CreativeFile> {
    return this.updateFileStatus(id, 'pending');
  }

  hasActiveUploads(files: CreativeFile[]): boolean {
    return files.some(file => 
      file.status === 'downloading' || 
      file.status === 'uploading' || 
      file.status === 'pending'
    );
  }

  getActiveUploadsCount(files: CreativeFile[]): number {
    return files.filter(file => 
      file.status === 'downloading' || 
      file.status === 'uploading' || 
      file.status === 'pending'
    ).length;
  }
}
