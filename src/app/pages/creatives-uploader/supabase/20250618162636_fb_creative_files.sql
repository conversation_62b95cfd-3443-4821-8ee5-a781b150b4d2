CREATE TABLE fb_creative_files
(
  id                   UUID                     DEFAULT gen_random_uuid() PRIMARY KEY,

  -- Google Drive основна информация
  google_drive_file_id VARCHAR(255)                                NOT NULL UNIQUE,
  file_name            VA<PERSON>HAR(255)                                NOT NULL,
  google_drive_url     TEXT                                        NOT NULL,
  file_size            BIGINT,
  mime_type            VARCHAR(100)                                NOT NULL,

  -- Статус на качването
  status               VARCHAR(20)              DEFAULT 'selected' NOT NULL,
  fb_creative_id       VARCHAR(100), -- ID от Facebook след успешно качване
  upload_error         TEXT,         -- Съобщение за грешка

  -- Timestamps
  created_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()      NOT NULL,
  updated_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()      NOT NULL,
  uploaded_at          TIMESTAMP WITH TIME ZONE,


  -- Constraints
  CONSTRAINT valid_status CHECK (status IN ('selected', 'downloading', 'uploading', 'uploaded', 'failed'))
);
