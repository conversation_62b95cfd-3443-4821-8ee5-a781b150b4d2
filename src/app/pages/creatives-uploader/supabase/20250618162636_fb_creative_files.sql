CREATE TABLE fb_creative_files
(
  id                   UUID                     DEFAULT gen_random_uuid() PRIMARY KEY,

  google_drive_file_id VARCHAR(255)                               NOT NULL UNIQUE,
  file_name            VARCHAR(255)                               NOT NULL,
  google_drive_url     TEXT                                       NOT NULL,
  file_size            BIGINT,
  mime_type            VARCHAR(100)                               NOT NULL,

  status               VARCHAR(20)              DEFAULT 'pending' NOT NULL,
  fb_creative_id       VARCHAR(100),
  upload_error         TEXT,

  created_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()     NOT NULL,
  updated_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()     NOT NULL,
  uploaded_at          TIMESTAMP WITH TIME ZONE,

  CONSTRAINT valid_status CHECK (status IN ('pending', 'downloading', 'uploading', 'uploaded', 'failed'))
);
