export interface CreativeFile {
  id: string;
  google_drive_file_id: string;
  file_name: string;
  file_size?: number;
  mime_type: string;
  status: CreativeFileStatus;
  fb_creative_id?: string;
  upload_error?: string;
  created_at: string;
  updated_at: string;
  uploaded_at?: string;
}

export type CreativeFileStatus =
  | 'pending'
  | 'downloading'
  | 'uploading'
  | 'uploaded'
  | 'failed';

export interface CreativeFileFilters {
  status?: CreativeFileStatus;
  mime_type?: string;
  search?: string;
}

export interface CreativeFileResponse {
  data: CreativeFile[];
  total_count: number;
}

export interface FilterOption {
  label: string;
  value: string;
}

export const CREATIVE_FILE_STATUSES: {
  label: string;
  value: CreativeFileStatus;
  severity: string;
}[] = [
  { label: 'Pending', value: 'pending', severity: 'secondary' },
  { label: 'Downloading', value: 'downloading', severity: 'info' },
  { label: 'Uploading', value: 'uploading', severity: 'warning' },
  { label: 'Uploaded', value: 'uploaded', severity: 'success' },
  { label: 'Failed', value: 'failed', severity: 'danger' },
];

export const MIME_TYPE_FILTERS: FilterOption[] = [
  { label: 'All Files', value: '' },
  { label: 'Videos', value: 'video/' },
  { label: 'Images', value: 'image/' },
];

export function getStatusSeverity(status: CreativeFileStatus): string {
  const statusConfig = CREATIVE_FILE_STATUSES.find((s) => s.value === status);
  return statusConfig?.severity || 'secondary';
}

export function getStatusLabel(status: CreativeFileStatus): string {
  const statusConfig = CREATIVE_FILE_STATUSES.find((s) => s.value === status);
  return statusConfig?.label || status;
}

export function formatFileSize(bytes?: number): string {
  if (!bytes) return 'N/A';

  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
}

export function getMimeTypeIcon(mimeType: string): string {
  if (mimeType.startsWith('video/')) return 'pi-video';
  if (mimeType.startsWith('image/')) return 'pi-image';
  return 'pi-file';
}

export function getMimeTypeLabel(mimeType: string): string {
  if (mimeType.startsWith('video/')) return 'Video';
  if (mimeType.startsWith('image/')) return 'Image';
  return 'File';
}

export function isActiveStatus(status: CreativeFileStatus): boolean {
  return status === 'downloading' || status === 'uploading';
}
