import type { PortalConfig } from './types';

export const portalConfig: PortalConfig = {
  supabase: {
    url: 'https://jthdagdrrxcyfsulrnql.supabase.co',
    anonKey:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0aGRhZ2RycnhjeWZzdWxybnFsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMjIzNDUsImV4cCI6MjA2NDc5ODM0NX0.FUogH3xIT8fnuAiVLnT1y6CsD_RY__EknRE4XRFVhHs',
  },
  features: [
    {
      id: 'ad-winners',
      name: 'Ad Winners',
      icon: 'pi pi-trophy',
      route: '/ad-winners',
      enabled: true,
    },
    {
      id: 'creatives-uploader',
      name: 'File Upload Automation',
      icon: 'pi pi-cloud-upload',
      route: '/creatives-uploader',
      enabled: true,
    },
  ],
};
